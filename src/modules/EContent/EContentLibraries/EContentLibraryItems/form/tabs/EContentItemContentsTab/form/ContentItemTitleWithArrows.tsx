import React, { useCallback, useMemo, useState } from 'react';
import { findIndex, get, isEmpty, isNumber } from 'lodash';
import { useQuery } from 'react-apollo';
import { useHistory, useLocation } from 'react-router-dom';

import eContentItemPreviousNextContents from '../../../../../../../../common/data/eContent/eContentItemPreviousNextContents.graphql';
import IEContentContent from '../../../../../../../../common/abstract/EContent/IEContentContent';
import getGqlOperationName from '../../../../../../../../common/utils/getGqlOperationName';
import { SEQUENCE } from '../../../../../../../../model/ContentSortingTypes';
import { Active } from '../../../../../../../../model/Statuses';
import ArrowButtonsTitle from './Question/ArrowButtonsTitle';
import useT from '../../../../../../../../common/components/utils/Translations/useT';

const ONE = 1;
const THREE = 3;

export interface IContentItemTitleWithArrows {
  contentIndex: number | null;
  itemId: number | undefined;
  mainItemId: number;
  searchQuery: string | undefined;
  contentFilters: any;
  updatedCount: number;
}
export default function ContentItemTitleWithArrows({
  contentIndex,
  itemId,
  mainItemId,
  searchQuery,
  contentFilters,
  updatedCount,
}: IContentItemTitleWithArrows) {
  const t = useT();
  const history = useHistory();
  const { pathname } = useLocation();
  const [count, setCount] = useState(updatedCount);
  const prevNextItemsResp = useQuery(eContentItemPreviousNextContents, {
    variables: {
      ...(isNumber(contentIndex) && contentIndex >= 0
        ? { ...contentFilters }
        : { sortOrder: SEQUENCE.value, status: [Active.value] }),
      ...(!isEmpty(searchQuery) ? { searchQuery } : {}),
      contentId: itemId,
      itemId: mainItemId,
      first:
        count === updatedCount && contentIndex && contentIndex >= 1
          ? contentIndex - 1
          : 0,
      count: THREE,
      hasContentIndex: count === updatedCount && isNumber(contentIndex),
      updatedCount,
    },
    skip: !itemId || !mainItemId,
  });

  const prevNextItems: IEContentContent[] = useMemo(
    () =>
      get(
        prevNextItemsResp,
        `data.${getGqlOperationName(eContentItemPreviousNextContents)}`,
        {},
      ),
    [prevNextItemsResp],
  );

  const prevItem = useMemo(() => {
    if (!isEmpty(prevNextItems)) {
      const idx = findIndex(prevNextItems, { id: itemId });
      if (prevNextItems[idx - 1]) return prevNextItems[idx - 1];
    }
    return null;
  }, [itemId, prevNextItems]);

  const nextItem = useMemo(() => {
    if (!isEmpty(prevNextItems)) {
      const idx = findIndex(prevNextItems, { id: itemId });
      if (prevNextItems[idx + 1]) return prevNextItems[idx + 1];
    }
    return null;
  }, [itemId, prevNextItems]);

  const openRelatedItem = useCallback(
    val => {
      const isNext = val > 0;
      const prms = {};
      if (searchQuery)
        Object.assign(prms, {
          searchQuery,
        });
      if (isNumber(contentIndex)) {
        Object.assign(prms, {
          contentIndex: (isNext
            ? contentIndex + 1
            : contentIndex - 1
          ).toString(),
        });
      }
      if (!isEmpty(contentFilters)) {
        Object.assign(prms, {
          contentFilters: JSON.stringify(contentFilters),
        });
      }
      const calcParams = !isEmpty(prms)
        ? new URLSearchParams(prms).toString()
        : '';
      const params = !isEmpty(calcParams) ? `?${calcParams}` : '';
      if (isNext && nextItem) {
        const contentUrl = pathname.replace(
          `${itemId}`,
          `${nextItem.id}${params}`,
        );
        history.push(contentUrl);
      } else if (prevItem) {
        const contentUrl = pathname.replace(
          `${itemId}`,
          `${prevItem.id}${params}`,
        );
        history.push(contentUrl);
      }
    },
    [
      pathname,
      nextItem,
      prevItem,
      searchQuery,
      itemId,
      history,
      contentIndex,
      contentFilters,
    ],
  );

  return (
    <ArrowButtonsTitle
      h5
      currentRecord={prevItem ? ONE : 0}
      setNewRecord={openRelatedItem}
      title={t('Content')}
      totalRecord={nextItem ? THREE : prevNextItems.length}
    />
  );
}
