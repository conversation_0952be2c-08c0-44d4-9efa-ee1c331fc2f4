import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { get, isEmpty, last, maxBy } from 'lodash';
import classNames from 'classnames';

import useT from '../../../../../../../../common/components/utils/Translations/useT';
import EntityFormArrayField from '../../../../../../../../common/components/containers/EntityForm/EntityFormArrayField';
import {
  Simple,
  Rich,
  SimpleAndRich,
} from '../../../../../../../../model/TextsType';
import { RichType, SimpleType } from '../../../../../../../../model/ClobsType';
import TextAreaField from '../../../../../../../../common/components/containers/EntityForm/fields/TextAreaField';
import TextEditorField from '../../../../../../../../common/components/containers/EntityForm/fields/TextEditorField';
import TagInputField from '../../../../../../../../common/components/containers/EntityForm/fields/TagInputField';
import useEntityFormContext from '../../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import { PanelButton } from '../../../../../../../../common/components/controls/PanelButtons';
import styles from './EContentItemContentForm.scss';
import Notifications from '../../../../../../../../common/utils/Notifications';
import Modal from '../../../../../../../../common/components/utils/Modal';
import TextTransform from './TextTransform';
import EntityForm from '../../../../../../../../common/components/containers/EntityForm';
import { IBasicEntity } from '../../../../../../../../common/components/containers/EntityForm/EntityForm';
import NumberField from '../../../../../../../../common/components/containers/EntityForm/fields/NumberField';
import { ONE, ZERO } from '../../../../../../../../common/const';
import { E_CONTENT_CONTENT_FILE } from '../../../../../../../../fsCategories';
import SelectBoxField from '../../../../../../../../common/components/containers/EntityForm/fields/SelectBoxField';
import { IEContentResourceCategory } from '../../../../../../../../common/abstract/EContent/IEContentResourceXAttribute';

export interface ITextSectionArrayField {
  textBoxesType?: number;
  minNumberOfTextBoxes: number;
  maxNumberOfTextBoxes: number;
  textCharactersNumber: number;
  textHeight: number;
  maxTagLength?: number;
  isKeywordRequired?: boolean;
  hasKeyword?: boolean;
  suggestions?: string[];
  isTransformEnabled?: boolean;
  clobsPreviewData?: Record<string, any>;
  cookedAttributes?: any;
  hasRenderedPreviewSimpleText?: boolean;
  hasRenderedPreviewRichText?: boolean;
  isContentCategoryEnabled: boolean;
  resourceCategoriesOptions: IEContentResourceCategory[];
}

const FIELD_NAME = 'cookedClobs.text';
const ROW_HEIGHT = 20;
const PADDING = 15;
const RICH_ROW_HEIGHT = 22;
const RICH_PADDING = 10;

const TextSectionArrayField: React.FC<ITextSectionArrayField> = ({
  textBoxesType = Simple.value,
  minNumberOfTextBoxes,
  maxNumberOfTextBoxes = 1,
  textCharactersNumber,
  textHeight,
  maxTagLength,
  isKeywordRequired,
  hasKeyword,
  suggestions,
  isTransformEnabled,
  clobsPreviewData,
  cookedAttributes,
  hasRenderedPreviewSimpleText,
  hasRenderedPreviewRichText,
  isContentCategoryEnabled,
  resourceCategoriesOptions,
}) => {
  const t = useT();
  const [showTextTransform, setShowTextTransform] = useState<boolean>(false);
  const [transformInput, setTransformInput] = useState<string>('');
  const [transformField, setTransformField] = useState<string>('');
  const [activeMember, setActiveMember] = useState<string>('');

  const { values, setFieldValue, isEditing } = useEntityFormContext();

  const currentNumber = useMemo(() => get(values, `${FIELD_NAME}.length`, 0), [
    values,
  ]);

  const [showTextTransformType, setShowTextTransformType] = useState<number>(
    textBoxesType === Rich.value ? RichType.value : SimpleType.value,
  );
  const [makingType, setMakingType] = useState<number>(
    textBoxesType === Rich.value ? RichType.value : SimpleType.value,
  );
  const [makingNew, setMakingNew] = useState<boolean>(false);
  const onMakeNewEntity = () => {
    setMakingNew(true);
  };

  useEffect(() => {
    const list = get(values, `${FIELD_NAME}`, []);
    const maxElement = maxBy<{ groupSequence: number }>(list, 'groupSequence');
    const maxSequence = maxElement?.groupSequence || 0;
    const lastItem = last<{ groupSequence: number }>(list);
    if (list.length > 0 && !(lastItem && lastItem.groupSequence) && makingNew) {
      setFieldValue(
        `${FIELD_NAME}.${list.length - ONE}.groupSequence`,
        Number(maxSequence) + 1,
      );
      setFieldValue(`${FIELD_NAME}.${list.length - ONE}.text`, '');
      setFieldValue(
        `${FIELD_NAME}.${list.length - ONE}.contentClobTypeId`,
        makingType,
      );
      setMakingNew(false);
    }
  }, [values, makingNew, makingType]);
  const contentRefs = useRef<any[]>([]);
  const [expandedIdx, setExpandedIdx] = useState(null);
  const [wasInitiallyOverflowing, setWasInitiallyOverflowing] = useState<any[]>(
    [],
  );
  const toggleExpanded = useCallback(
    idx => {
      setExpandedIdx(expandedIdx === idx ? null : idx);
    },
    [expandedIdx],
  );
  const [isOverflowing, setIsOverflowing] = useState([]);

  const label = useMemo(() => {
    let heading = 'Texts';
    if (maxNumberOfTextBoxes > 1) {
      heading = `${heading} (${currentNumber} out of ${maxNumberOfTextBoxes})`;
    }
    return heading;
  }, [maxNumberOfTextBoxes, currentNumber]);

  const onActionCopyProcess = useCallback(
    (val: string) => {
      navigator.clipboard
        .writeText(val)
        .then(() => {
          Notifications.success(t('Copied to Clipboard'), '', t);
        })
        .catch(error => {
          console.error('Error copying text: ', error);
        });
    },
    [t],
  );
  const onActionCopy = useCallback(
    (name: string) => {
      const val = get(values, name, 0)
        .replace(/<\s*\/?br\s*[\/]?>/gi, '\n')
        .replace(/&[^\s]+?;/g, '')
        .replace(/<\/p>/g, '</p> \n')
        .replace(/<[^>]+>/g, '');
      onActionCopyProcess(val);
    },
    [values, onActionCopyProcess],
  );
  const onActionCopyFormatted = useCallback(
    (name: string) => {
      const val = get(values, name, 0);
      onActionCopyProcess(val);
    },
    [values, onActionCopyProcess],
  );
  const onActionTransform = useCallback(
    (name: string, textBoxType: number) => {
      let val = get(values, name, 0) || '';
      if (val && textBoxType !== SimpleType.value) {
        val = val
          .replace(/<\s*\/?br\s*[\/]?>/gi, '\n')
          .replace(/&[^\s]+?;/g, '')
          .replace(/<\/p>/g, '</p> \n')
          .replace(/<[^>]+>/g, '');
      }
      setTransformInput(val);
      setTransformField(name);
      setShowTextTransform(true);
      setShowTextTransformType(textBoxType);
    },
    [values],
  );
  const onTransformCancel = useCallback(() => {
    setShowTextTransform(false);
  }, []);
  const onTransformCopy = useCallback(
    (value: string) => {
      onActionCopyProcess(value);
    },
    [onActionCopy, onTransformCancel],
  );

  function trimAndConvertNewlines(inputString: string) {
    const newlinePositions: number[] = [];
    for (let i = 0, len = inputString.length; i < len; i++) {
      if (inputString[i] === '\n') {
        newlinePositions.push(i);
      }
    }
    const convertedString = inputString.replace(/\n/g, '<br>');
    return convertedString;
  }

  const onTransformUpdate = useCallback(
    (value: string) => {
      const formatted =
        showTextTransformType === SimpleType.value
          ? value
          : trimAndConvertNewlines(value);
      setFieldValue(transformField, formatted);
      onTransformCancel();
      Notifications.success(t('Updated Successfully'), '', t);
    },
    [
      setFieldValue,
      transformField,
      onTransformCancel,
      t,
      showTextTransformType,
    ],
  );
  const onTransformAdd = useCallback(
    (value: string) => {
      const formatted =
        showTextTransformType === SimpleType.value
          ? value
          : trimAndConvertNewlines(value);
      setFieldValue(transformField, formatted);
      Notifications.success(t('Added Successfully'), '', t);
    },
    [setFieldValue, transformField, t, showTextTransformType],
  );

  const addButtonList = useCallback(
    (name: string, textBoxType: number) => {
      const val = get(values, name, 0);
      const onClickCopy = function () {
        onActionCopy(name);
      };
      const onClickCopyFormatted = function () {
        onActionCopyFormatted(name);
      };
      const onClickTransform = function () {
        onActionTransform(name, textBoxType);
      };
      return (
        <div className={classNames('mr-10')}>
          {!isEmpty(val) &&
            (textBoxType === SimpleType.value ? (
              <PanelButton
                key="copy"
                badge={t('Copy')}
                className={classNames(styles.roundButton, 'mb-5')}
                icon=""
                title={t(`Copy to Clipboard`)}
                onClick={onClickCopy}
              />
            ) : (
              <>
                <PanelButton
                  key="copy"
                  badge={t('Copy Simple')}
                  className={classNames(styles.roundButton, 'mb-5')}
                  icon=""
                  title={t(`Copy to Clipboard`)}
                  onClick={onClickCopy}
                />
                <PanelButton
                  key="copyformated"
                  badge={t('Copy Formatted')}
                  className={classNames(styles.roundButton, 'mb-5')}
                  icon=""
                  title={t(`Copy to Clipboard`)}
                  onClick={onClickCopyFormatted}
                />
              </>
            ))}

          {isTransformEnabled && (
            <PanelButton
              key="transform"
              badge={t('Transform')}
              className={classNames(styles.roundButton, 'mb-5')}
              icon=""
              title={t(`Transform`)}
              onClick={onClickTransform}
            />
          )}
        </div>
      );
    },
    [
      onActionCopy,
      onActionTransform,
      values,
      isTransformEnabled,
      onActionCopyFormatted,
      t,
    ],
  );
  const renderSubmitSectionEmpty = useCallback(props => <></>, []);

  useEffect(() => {
    const handleResize = (entry, idx) => {
      const { clientHeight, scrollHeight } = entry.target;
      setIsOverflowing(prev => {
        const newOverflowing: any = [...prev];
        const isCurrentlyOverflowing = scrollHeight > clientHeight;

        if (expandedIdx !== idx) {
          newOverflowing[idx] = isCurrentlyOverflowing;
        }

        if (!wasInitiallyOverflowing[idx]) {
          setWasInitiallyOverflowing(prevInitial => {
            const newInitialOverflowing = [...prevInitial];
            newInitialOverflowing[idx] = isCurrentlyOverflowing;
            return newInitialOverflowing;
          });
        }

        return newOverflowing;
      });
    };

    const resizeObservers = contentRefs.current.map((ref, idx) => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      const observer = new ResizeObserver(entries => {
        entries.forEach(entry => handleResize(entry, idx));
      });
      if (ref) observer.observe(ref);
      return observer;
    });

    return () => {
      resizeObservers.forEach(observer => observer.disconnect());
    };
  }, [expandedIdx, currentNumber]);

  const actionList = [
    {
      id: SimpleType.value,
      name: t('Simple Text Box'),
      value: SimpleType.value.toString(),
    },
    {
      id: RichType.value,
      name: t('Rich Text Box'),
      value: RichType.value.toString(),
    },
  ];

  const onActionAdd = useCallback((id, index) => {
    setMakingType(id);
  }, []);

  return (
    <>
      <EntityFormArrayField
        deletable
        isSubsection
        removeRightButton
        buttonDropdown={
          textBoxesType === SimpleAndRich.value
            ? {
                actionList,
                onActionAdd,
              }
            : undefined
        }
        buttonTitle={t('Add Text')}
        isScrollToNew={false}
        isStrikeButtonDisabled={currentNumber >= maxNumberOfTextBoxes}
        label={t(label)}
        maxItemsCount={maxNumberOfTextBoxes}
        minItemsCount={minNumberOfTextBoxes}
        name={FIELD_NAME}
        required={minNumberOfTextBoxes > 0}
        onMakeNewEntity={onMakeNewEntity}
      >
        {(member, index) => {
          let textBoxType = get(values, `${member}.contentClobTypeId`);
          if (!textBoxType) {
            if (textBoxesType === Simple.value) textBoxType = SimpleType.value;
            if (textBoxesType === Rich.value) textBoxType = RichType.value;
          }
          const maxHeight = () => {
            const rowHeight =
              textBoxType === SimpleType.value ? ROW_HEIGHT : RICH_ROW_HEIGHT;
            const padding =
              textBoxType === SimpleType.value ? PADDING : RICH_PADDING;
            return textHeight ? textHeight * rowHeight + padding : 'unset';
          };
          const _handleMouseOver = function () {
            setActiveMember(member);
          };
          const _handleMouseLeave = function () {
            setActiveMember('');
          };
          const _toggleExpanded = function () {
            toggleExpanded(index);
          };
          return (
            <div
              onMouseLeave={_handleMouseLeave}
              onMouseOver={_handleMouseOver}
            >
              <div className={styles.mouseButtonContainer}>
                <div>
                  <NumberField
                    noLabel
                    required
                    columns={10}
                    label={t('Sequence')}
                    min={1}
                    name={`${member}.groupSequence`}
                  />
                  {isContentCategoryEnabled ? (
                    <SelectBoxField
                      columns={10}
                      itemTitlePropName="name"
                      itemValuePropName="id"
                      label={t('Content Category')}
                      name={`${member}.resourceCategoryId`}
                      options={resourceCategoriesOptions}
                      returnType="number"
                      withTitle={false}
                    />
                  ) : null}
                </div>

                {!!isEditing &&
                  activeMember === member &&
                  addButtonList(`${member}.text`, textBoxType)}
              </div>
              <>
                <div
                  key={index}
                  ref={el => (contentRefs.current[index] = el)} // Assign ref dynamically for each element
                  className={classNames(styles.textContentContainer, 'w-100')}
                  // eslint-disable-next-line react/forbid-dom-props
                  style={{
                    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
                    // @ts-ignore
                    maxHeight:
                      isEditing || expandedIdx === index
                        ? 'unset'
                        : maxHeight(),
                  }}
                >
                  {textBoxType === SimpleType.value ? (
                    <TextAreaField
                      autoHeight
                      noLabel
                      noViewLabel
                      required
                      clobsPreviewData={clobsPreviewData}
                      columns={1}
                      hasRenderedPreview={hasRenderedPreviewSimpleText}
                      isScroll={!isEditing ? false : true}
                      label={t('Text Content')}
                      maxLength={textCharactersNumber}
                      name={`${member}.text`}
                      rows={textHeight}
                    />
                  ) : (
                    <TextEditorField
                      isExtended
                      noLabel
                      required
                      clobsPreviewData={clobsPreviewData}
                      columns={1}
                      cookedAttributes={cookedAttributes}
                      fileCategory={E_CONTENT_CONTENT_FILE}
                      hasRenderedPreview={hasRenderedPreviewRichText}
                      label={t('Text Content')}
                      maxLength={0}
                      name={`${member}.text`}
                      rows={textHeight}
                      wrapperClassNames={{
                        1: 'col-lg-12 col-md-12 col-sm-12 col-xs-12',
                      }}
                    />
                  )}
                </div>
                {!isEditing && wasInitiallyOverflowing[index] && (
                  <div className="m-10">
                    <a onClick={_toggleExpanded}>
                      {expandedIdx === index ? 'Show Less' : 'Show All'}
                    </a>
                  </div>
                )}
              </>
              {hasKeyword ? (
                <TagInputField
                  noLabel
                  columns={1}
                  label={t('Content Keyword')}
                  maxTagLength={maxTagLength}
                  name={`${member}.contentKeyword`}
                  required={isKeywordRequired}
                  suggestions={suggestions}
                />
              ) : null}
            </div>
          );
        }}
      </EntityFormArrayField>
      <Modal
        className={classNames(styles.transformModelBox)}
        size="lg"
        title={t('Transformation')}
        visible={showTextTransform}
        onClose={onTransformCancel}
      >
        <EntityForm
          entity={{} as IBasicEntity}
          entityName="QuestionAutoGenerate"
          submitSectionRenderer={renderSubmitSectionEmpty}
        >
          <TextTransform
            maxLength={
              showTextTransformType === SimpleType.value
                ? textCharactersNumber
                : ZERO
            }
            transformInput={transformInput}
            onCancel={onTransformCancel}
            onTransformAdd={onTransformAdd}
            onTransformCopy={onTransformCopy}
            onTransformUpdate={onTransformUpdate}
          />
        </EntityForm>
      </Modal>
    </>
  );
};

export default TextSectionArrayField;
