import React, { useEffect, useMemo } from 'react';
import { get, last, maxBy } from 'lodash';

import useT from '../../../../../../../../common/components/utils/Translations/useT';
import EntityFormArrayField from '../../../../../../../../common/components/containers/EntityForm/EntityFormArrayField';
import TextField from '../../../../../../../../common/components/containers/EntityForm/fields/TextField';
import { MANDATORY, TRuleType } from '../../../../../../../../model/RuleType';
import TagInputField from '../../../../../../../../common/components/containers/EntityForm/fields/TagInputField';
import { MAX_NAME_LENGTH } from '../../../../../../../../common/components/containers/EntityForm/fields/const/lengths';
import useEntityFormContext from '../../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import UrlSectionArrayFieldView from './UrlSectionArrayFieldView';
import NumberField from '../../../../../../../../common/components/containers/EntityForm/fields/NumberField';
import { ONE } from '../../../../../../../../common/const';
import UrlSectionArrayFieldSet from './UrlSectionArrayFieldViewSet';
import { IEContentResourceCategory } from '../../../../../../../../common/abstract/EContent/IEContentResourceXAttribute';
import SelectBoxField from '../../../../../../../../common/components/containers/EntityForm/fields/SelectBoxField';

const FIELD_NAME = 'cookedClobs.url';

export interface IUrlSectionArrayField {
  minNumberOfUrls: number;
  maxNumberOfUrls: number;
  urlCaptionRule?: TRuleType;
  hasCaption: boolean;
  maxTagLength?: number;
  isKeywordRequired?: boolean;
  hasKeyword?: boolean;
  suggestions?: string[];
  isContentCategoryEnabled: boolean;
  resourceCategoriesOptions: IEContentResourceCategory[];
}

const UrlSectionArrayField: React.FC<IUrlSectionArrayField> = ({
  minNumberOfUrls,
  maxNumberOfUrls,
  hasCaption,
  urlCaptionRule,
  maxTagLength,
  isKeywordRequired,
  hasKeyword,
  suggestions,
  isContentCategoryEnabled,
  resourceCategoriesOptions,
}) => {
  const t = useT();

  const isCaptionRequired = useMemo(() => urlCaptionRule === MANDATORY.value, [
    urlCaptionRule,
  ]);

  const { values, setFieldValue, isEditing } = useEntityFormContext();

  const currentNumber = useMemo(() => get(values, `${FIELD_NAME}.length`, 0), [
    values,
  ]);
  useEffect(() => {
    const list = get(values, `${FIELD_NAME}`, []);
    const maxElement = maxBy<{ groupSequence: number }>(list, 'groupSequence');
    const maxSequence = maxElement?.groupSequence || 0;
    const lastItem = last<{ groupSequence: number }>(list);
    if (list.length > 0 && !(lastItem && lastItem.groupSequence)) {
      setFieldValue(
        `${FIELD_NAME}.${list.length - ONE}.groupSequence`,
        Number(maxSequence) + ONE,
      );
    }
  }, [values]);

  const label = useMemo(() => {
    let heading = 'URLs';
    if (maxNumberOfUrls > 1) {
      heading = `${heading} (${currentNumber} out of ${maxNumberOfUrls})`;
    }
    return heading;
  }, [maxNumberOfUrls, currentNumber]);

  return (
    <EntityFormArrayField
      deletable
      isSubsection
      removeRightButton
      buttonTitle={t('Add URL')}
      isScrollToNew={false}
      isStrikeButtonDisabled={currentNumber >= maxNumberOfUrls}
      label={t(label)}
      maxItemsCount={maxNumberOfUrls}
      minItemsCount={minNumberOfUrls}
      name={FIELD_NAME}
      required={minNumberOfUrls > 0}
      onMakeNewEntity={onMakeNewEntity}
    >
      {member => (
        <>
          <NumberField
            noLabel
            required
            columns={10}
            label={t('Sequence')}
            min={1}
            name={`${member}.groupSequence`}
          />
          {isContentCategoryEnabled ? (
            <SelectBoxField
              isEmptyValueAllowed
              columns={10}
              itemTitlePropName="name"
              itemValuePropName="id"
              label={t('Content Category')}
              name={`${member}.resourceCategoryId`}
              options={resourceCategoriesOptions}
              returnType="number"
              withTitle={false}
            />
          ) : null}
          {isEditing ? (
            <UrlSectionArrayFieldSet
              required
              columns={1}
              label={t('URL')}
              name={`${member}.url`}
            />
          ) : (
            <UrlSectionArrayFieldView
              columns={1}
              label={t('URL')}
              name={`${member}.url`}
            />
          )}
          <TextField
            columns={1}
            label={t('URL name')}
            maxLength={MAX_NAME_LENGTH}
            name={`${member}.urlName`}
          />
          {hasCaption ? (
            <TextField
              columns={1}
              label={t('URL Caption')}
              maxLength={200}
              name={`${member}.urlCaption`}
              required={isCaptionRequired}
            />
          ) : null}
          {hasKeyword ? (
            <TagInputField
              columns={1}
              label={t('Content Keyword')}
              maxTagLength={maxTagLength}
              name={`${member}.urlKeyword`}
              required={isKeywordRequired}
              suggestions={suggestions}
            />
          ) : null}
        </>
      )}
    </EntityFormArrayField>
  );
};

const onMakeNewEntity = () => ({});

export default UrlSectionArrayField;
