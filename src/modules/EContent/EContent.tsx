import { loadable } from '../../common/utils/lazyLoading';
import structure from './structures/menu';

const EContentPage = loadable(() => import('./EContentPage'));

export default {
  id: 'e_content',
  title: 'eContent1', // This is a translation key that will be translated by t() in the UI components
  icon: 'books',
  route: 'e-content',
  defaultRoute: 'e-content/libraries',
  component: EContentPage,
  group: 'administration',
  structure,
};
