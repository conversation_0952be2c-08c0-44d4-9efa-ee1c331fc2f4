import { loadable } from '../../common/utils/lazyLoading';
import useT from '../../common/components/utils/Translations/useT';
import structure from './structures/menu';

const EContentPage = loadable(() => import('./EContentPage'));

export const useEContentModule = () => {
  const t = useT();

  return {
    id: 'e_content',
    title: t('eContent1'),
    icon: 'books',
    route: 'e-content',
    defaultRoute: 'e-content/libraries',
    component: EContentPage,
    group: 'administration',
    structure,
  };
};

// Default export for backward compatibility
export default useEContentModule;
// {
//   id: 'e_content',
//   title: 'eContent',
//   icon: 'books',
//   route: 'e-content',
//   defaultRoute: 'e-content/libraries',
//   component: EContentPage,
//   group: 'administration',
//   structure,
// };
