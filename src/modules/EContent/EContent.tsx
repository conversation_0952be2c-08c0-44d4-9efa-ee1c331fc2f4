import { useTranslation } from '../../common/components/utils/Translations';
import useT from '../../common/components/utils/Translations/useT';
import { loadable } from '../../common/utils/lazyLoading';
import structure from './structures/menu';

const EContentPage = loadable(() => import('./EContentPage'));
const t = useTranslation();

export default {
  id: 'e_content',
  title: t('eContent1'),
  icon: 'books',
  route: 'e-content',
  defaultRoute: 'e-content/libraries',
  component: EContentPage,
  group: 'administration',
  structure,
};
