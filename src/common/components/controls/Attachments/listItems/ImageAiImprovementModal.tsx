import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useQuery } from 'react-apollo';
import { filter, get, isEmpty, isNumber, map, orderBy } from 'lodash';
import classNames from 'classnames';
import RoundedPrimaryButton from '../../RoundedPrimaryButton';
import { IFileAttachmentTemp } from '../../../../abstract/IFileAttachments';
import RoundedLinkButton from '../../RoundedLinkButton';
import Modal from '../../../utils/Modal';
import useT from '../../../utils/Translations/useT';
import styles from './ImageAiImprovementModal.scss';
import Spinner from '../../../utils/Spinner';
import Notifications from '../../../../utils/Notifications';
import Scroll from '../../../../../common/components/other/Scroll';
import TextAreaField from '../../../containers/EntityForm/fields/TextAreaField';
import getGqlOperationName from '../../../../utils/getGqlOperationName';
import dalleQueryResultGql from '../../../../../common/data/eContent/dalleQueryResult.graphql';
import eContentSetting from '../../../../../common/data/eContent/eContentSetting.graphql';
import {
  GPT_VERSION,
  IMAGE_AI_IMPROVEMENT,
  REPLICATE_VERSION,
} from '../../../../../model/SettingType';
import { PanelButton } from '../../PanelButtons';
import SelectBoxField from '../../../containers/EntityForm/fields/SelectBoxField';
import { IGptVersion } from '../../../../../modules/EContent/EContentLibraries/EContentLibraryItems/form/tabs/EContentItemContentsTab/form/TextTransform';
import useEntityFormContext from '../../../containers/EntityForm/internal/useEntityFormContext';
import FilePreview from '../../UploadFileDialog/FilePreview';
import Radio from '../../base/Radio';
import useFilterStore from '../../FilterBar/hooks/useFilterStore';

const FIFTEEN = 15;
const PROMPT_TEXT_LENGTH1 = 1000;
const THREE = 3;
const BUTTON_MORE_NUMBER = 5;
const DEFAULT_REPLICATE_MODEL = 'black-forest-labs/flux-kontext-dev';
const STORE_KEY = 'E_CONTENT_IMAGE_IMPROVEMENT_REPLICATE_VERSION';
const CANCELLATION_RESET_DELAY = 100;

interface IImageAiImprovementModal {
  isModalVisible: boolean;
  attachment: IFileAttachmentTemp;
  onSubmit: (selectedImage: string) => Promise<void>;
  setModalVisibility: (isVisible: boolean) => void;
}

const ImageAiImprovementModal: React.FC<IImageAiImprovementModal> = ({
  onSubmit,
  attachment,
  isModalVisible = false,
  setModalVisibility,
}) => {
  const t = useT();

  const {
    values: defaultFilterValues = {
      version: DEFAULT_REPLICATE_MODEL,
    },
    onChange: onDefaultFilterValuesChange,
  } = useFilterStore(STORE_KEY, {
    defaultValues: {
      version: DEFAULT_REPLICATE_MODEL,
    },
  });

  const [selectedButton, setSelectedButton] = useState(0);
  const [aiModel, setAiModel] = useState<string>();
  const { values, setFieldValue } = useEntityFormContext();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [responseText, setResponseText] = useState<string[]>([]);
  const [counter, setCounter] = useState<number>(0);
  const [selectedImage, setSelectedImage] = useState<string>();
  const [uploading, setUploading] = useState<boolean>(false);
  const [numberOfitemsShown, setNumberOfItemsToShown] = useState(
    BUTTON_MORE_NUMBER,
  );
  const [loadingCustomButton, setLoadingCustomButton] = useState<number | null>(
    null,
  );
  const [isRequestCancelled, setIsRequestCancelled] = useState<boolean>(false);

  const handleRadioChange = useCallback((val: string) => {
    setSelectedImage(val);
  }, []);

  const onClearInput = useCallback(() => {
    setFieldValue('inputAuto', '');
  }, [setFieldValue]);
  const onClearImages = useCallback(() => {
    setSelectedImage(undefined);
    setResponseText([]);
  }, []);

  const windowHeight = useMemo(() => {
    const six = 0.5;
    const two = 0.2;
    const twoTwo = 0.23;
    const twoFive = 0.25;
    const four = 0.4;
    const fourFive = 0.45;
    const seven = 0.6;
    const fifty = 60;
    const height = window.innerHeight;
    setNumberOfItemsToShown(Math.round((six * height) / fifty));
    return {
      h20: Math.round(two * height),
      h22: Math.round(twoTwo * height),
      h25: Math.round(twoFive * height),
      h40: Math.round(four * height),
      h45: Math.round(fourFive * height),
      h60: Math.round(six * height),
      h70: Math.round(seven * height),
    };
  }, []);

  const onChangeVersion = useCallback(
    item => {
      setAiModel(item);
      onDefaultFilterValuesChange({ version: item });
    },
    [onDefaultFilterValuesChange],
  );

  const resetModalState = useCallback(() => {
    setIsRequestCancelled(true);
    onClearInput();
    onClearImages();
    setSelectedButton(0);
    setLoadingCustomButton(null);
    setCounter(0);
    setUploading(false);
    setNumberOfItemsToShown(BUTTON_MORE_NUMBER);
    setSearchQuery('');
    setTimeout(() => setIsRequestCancelled(false), CANCELLATION_RESET_DELAY);
  }, [onClearInput, onClearImages]);

  const handleModalClose = useCallback(() => {
    setModalVisibility(false);
    resetModalState();
  }, [setModalVisibility, resetModalState]);

  useEffect(() => {
    if (isModalVisible) {
      resetModalState();
    }
  }, [isModalVisible, resetModalState]);

  const {
    data: dataVersion,
    error: errorVersion,
    loading: loadingVersion,
  } = useQuery(eContentSetting, {
    variables: { settingType: REPLICATE_VERSION, searchQuery: '' },
    skip: isEmpty(GPT_VERSION),
  });

  const versionItems = useMemo(() => {
    if (dataVersion) {
      const field = getGqlOperationName(eContentSetting);
      const data = get(dataVersion, field, '');
      const res = get(data, 'response', []);
      return orderBy(
        map(res, item => {
          const contentList = item.split('\n');
          const itemObj: IGptVersion = {
            name: contentList[0].split('=').pop().trim(),
            value: contentList[1].split('=').pop().trim(),
            sequence: Number(contentList[2].split('=').pop().trim()),
            default: contentList[3].split('=').pop().trim() === 'true',
          };
          return itemObj;
        }),
        'sequence',
      );
    }
    return [];
  }, [dataVersion]);

  const replicateVersion = useMemo(() => {
    const value =
      defaultFilterValues.version ||
      versionItems?.find(x => !!x.default)?.value ||
      '';
    setFieldValue('replicateVersion', value);
    return value as string;
  }, [versionItems, defaultFilterValues.version, setFieldValue]);

  const { data: queryRes, error, loading } = useQuery(dalleQueryResultGql, {
    variables: {
      searchQuery,
      version: replicateVersion,
      counter,
      imageUrl: attachment.url,
    },
    skip:
      isEmpty(searchQuery) || isEmpty(replicateVersion) || isRequestCancelled,
  });

  useEffect(() => {
    if (!loading && queryRes && !isRequestCancelled) {
      setSearchQuery('');
      setLoadingCustomButton(null);
      const field = getGqlOperationName(dalleQueryResultGql);
      const data = get(queryRes, field, '');
      if (!data) return;
      const jsonStr = get(data, 'response', '');
      if (!jsonStr) {
        Notifications.error(t('Replicate API key is not working.'), '', t);
      }
      setResponseText(prev => [jsonStr, ...prev]);
    }
  }, [queryRes, error, t, replicateVersion, loading, isRequestCancelled]);

  useEffect(() => {
    if (error && !loading && !isRequestCancelled) {
      setSearchQuery('');
      setLoadingCustomButton(null);
      if (error.message.includes('API key not valid')) {
        Notifications.error(t('Replicate API key is not working.'), '', t);
      } else if (
        error.message.includes('payment method') ||
        error.message.includes('billing')
      ) {
        Notifications.error(t('API credit has expired.'), '', t);
      } else {
        Notifications.error(
          t('Data fetch failed, Please try again later!'),
          '',
          t,
        );
      }
    }
  }, [error, t, replicateVersion, loading, isRequestCancelled]);

  const { data: settingRes, error: errorSetting } = useQuery(eContentSetting, {
    variables: { settingType: IMAGE_AI_IMPROVEMENT, searchQuery: 'button' },
    skip: isEmpty(IMAGE_AI_IMPROVEMENT),
  });
  const settingItems = useMemo(() => {
    if (settingRes) {
      const field = getGqlOperationName(eContentSetting);
      const data = get(settingRes, field, '');
      const res = get(data, 'response', []);

      return orderBy(
        map(res, (item, index: number) => {
          const contentList = item.split('\n');
          let itemObj = {
            button: '',
            id: 0,
            description: 0,
            prompt: '',
          };
          if (contentList.length >= THREE) {
            itemObj = {
              id: index + 1,
              button: contentList[0].split('=').pop().trim(),
              description: contentList[1].split('=').pop().trim(),
              prompt: contentList[2].split('=').pop().trim(),
            };
          }
          return itemObj;
        }),
        'sequence',
      );
    }
    return [];
  }, [settingRes]);

  const handleSubmit = useCallback(async () => {
    setUploading(true);
    await onSubmit(selectedImage as string);
    setUploading(false);
    handleModalClose();
  }, [onSubmit, selectedImage, handleModalClose]);

  const renderModalFooter = useCallback(
    () => (
      <React.Fragment>
        <hr className={styles.modalHr} />
        <RoundedLinkButton
          additionClasses="mt-5 no-padding-left"
          onClick={handleModalClose}
        >
          {t('Cancel')}
        </RoundedLinkButton>
        <RoundedPrimaryButton
          additionClasses="mt-5"
          disabled={isEmpty(responseText) || !selectedImage || uploading}
          onClick={handleSubmit}
        >
          {uploading ? <Spinner inline /> : t('Attach')}
        </RoundedPrimaryButton>
      </React.Fragment>
    ),
    [responseText, selectedImage, handleModalClose, t, handleSubmit, uploading],
  );

  const replicateVersionDropdown = useCallback(
    () => (
      <div className="w-100">
        <SelectBoxField
          columns={0}
          itemTitlePropName="name"
          itemValuePropName="value"
          label={t('Version')}
          name="replicateVersion"
          options={versionItems}
          onChange={onChangeVersion}
        />
      </div>
    ),
    [versionItems, onChangeVersion, t],
  );

  const showMore = useCallback(() => {
    if (numberOfitemsShown + BUTTON_MORE_NUMBER <= settingItems.length) {
      setNumberOfItemsToShown(numberOfitemsShown + BUTTON_MORE_NUMBER);
    } else {
      setNumberOfItemsToShown(settingItems.length);
    }
  }, [numberOfitemsShown, settingItems]);

  const generateImage = useCallback(
    (buttonId?: number) => {
      let prompt = '';
      if (buttonId && isNumber(buttonId)) {
        const item = filter(
          settingItems,
          element => element.id === buttonId,
        ).pop();
        prompt = item?.prompt || '';
        setLoadingCustomButton(buttonId);
        setSelectedButton(buttonId);
      } else {
        setLoadingCustomButton(null);
      }
      const query = `${values?.inputAuto || ''} ${prompt}`;
      query === searchQuery && setCounter(counter => counter + 1);
      query && setSearchQuery(query);
    },
    [searchQuery, setSearchQuery, settingItems, values],
  );

  const addButtonList = useMemo(
    () => (
      <div className={styles.centerBox}>
        <Scroll autoHeightMax={windowHeight.h45}>
          <div className={styles.centerBox}>
            {map(settingItems.slice(0, numberOfitemsShown), element => {
              const onClickButton = function () {
                generateImage(element.id);
              };
              const isThisButtonLoading =
                loading && loadingCustomButton === element.id;
              const isAnyLoading =
                (loading && loadingCustomButton !== null) ||
                (loading && loadingCustomButton === null);

              return isThisButtonLoading ? (
                <div key={element.id} className="mb-10 mt-10 no-margin-left">
                  <Spinner inline />
                </div>
              ) : (
                <PanelButton
                  key={element.id}
                  badge={t(element.button)}
                  className={classNames(
                    'mb-10 mt-10 no-margin-left',
                    selectedButton === element.id
                      ? styles.active
                      : styles.inactive,
                  )}
                  icon=""
                  isDisabled={isAnyLoading}
                  title={t((element.description as unknown) as string)}
                  onClick={onClickButton}
                />
              );
            })}
          </div>
        </Scroll>
        <RoundedLinkButton
          additionClasses={classNames(
            'btn btn-link legitRipple mr-5',
            settingItems &&
              numberOfitemsShown >= settingItems.length &&
              'invisible',
          )}
          type="button"
          onClick={showMore}
        >
          {t('More')}
        </RoundedLinkButton>
        {replicateVersionDropdown()}
        <RoundedPrimaryButton
          additionClasses="mt-5"
          disabled={
            loadingCustomButton !== null ||
            (loading && loadingCustomButton === null) ||
            isEmpty(values?.inputAuto)
          }
          onClick={generateImage}
        >
          {loading && loadingCustomButton === null ? (
            <Spinner inline />
          ) : (
            t('Generate')
          )}
        </RoundedPrimaryButton>
      </div>
    ),
    [
      generateImage,
      settingItems,
      selectedButton,
      loadingCustomButton,
      loading,
      replicateVersionDropdown,
      numberOfitemsShown,
      showMore,
      t,
      windowHeight.h45,
      values,
    ],
  );

  return (
    <Modal
      renderFooter={renderModalFooter}
      size="lg"
      title={t('Image AI Improvement')}
      visible={isModalVisible}
      onClose={handleModalClose}
    >
      <div>
        <div>
          <div className={styles.flexClear}>
            <RoundedLinkButton
              additionClasses="btn btn-link legitRipple btn-xs pull-left"
              type="button"
              onClick={onClearInput}
            >
              {t('Clear')}
            </RoundedLinkButton>
            <RoundedLinkButton
              additionClasses="btn btn-link legitRipple btn-xs pull-riht"
              type="button"
              onClick={onClearImages}
            >
              {t('Clear')}
            </RoundedLinkButton>
          </div>
          <div className={classNames(styles.questionTop)}>
            <div
              className={classNames(
                styles.col_40,
                'col-md-4 col-md-4 col-sm-4 col-xs-12',
                'p-10',
                styles.autoTextBox,
                styles.border,
              )}
            >
              <Scroll autoHeightMax={windowHeight.h70}>
                <TextAreaField
                  autoHeight
                  noLabel
                  className={styles.h60}
                  columns={1}
                  maxLength={PROMPT_TEXT_LENGTH1}
                  name="inputAuto"
                  placeholder={t('Enter prompt')}
                  rows={FIFTEEN}
                />
              </Scroll>
            </div>
            <div
              className={classNames(
                styles.col_10,
                'col-md-2 col-sm-3 col-xs-12',
                'p-10',
                styles.border,
              )}
            >
              {addButtonList}
            </div>
            <div
              className={classNames(
                styles.col_50,
                'col-md-6 col-sm-5 col-xs-12',
                'p-5',
                styles.border,
              )}
            >
              <Scroll autoHeightMax={windowHeight.h70}>
                <div className="">
                  {map(responseText, image => (
                    <div className={styles.flex}>
                      <div className="col-lg-1">
                        <Radio
                          value={selectedImage === image}
                          // eslint-disable-next-line react/jsx-no-bind
                          onChange={() => handleRadioChange(image)}
                        />
                      </div>
                      <div className="col-lg-8">
                        <div className={styles.wrapper}>
                          <div className={styles.card}>
                            <a href={image}>
                              <FilePreview
                                hideDetail
                                hideFilename
                                columns={4}
                                // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
                                // @ts-ignore
                                file={{ url: image }}
                                hasCrop={false}
                                hasRemove={false}
                              />
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Scroll>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ImageAiImprovementModal;
